/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #212121;
    color: #ffffff;
    height: 100vh;
    overflow: hidden;
}

/* 顶部导航栏 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #171717;
    border-bottom: 1px solid #404040;
    height: 60px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: background-color 0.2s;
}

.sidebar-toggle:hover {
    background-color: #404040;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: 600;
}

.logo i {
    color: #10a37f;
    font-size: 20px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 12px;
}

.upgrade-btn {
    background: linear-gradient(135deg, #10a37f, #0d8f6b);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    transition: transform 0.2s;
}

.upgrade-btn:hover {
    transform: translateY(-1px);
}

.settings-btn, .user-avatar {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 16px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.settings-btn:hover, .user-avatar:hover {
    background-color: #404040;
}

/* 主容器 */
.main-container {
    display: flex;
    height: 100vh;
    padding-top: 60px;
}

/* 侧边栏 */
.sidebar {
    width: 260px;
    background-color: #171717;
    border-right: 1px solid #404040;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;
}

.sidebar-header {
    padding: 16px;
    border-bottom: 1px solid #404040;
}

.new-chat-btn {
    width: 100%;
    background: none;
    border: 1px solid #404040;
    color: #ffffff;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    transition: all 0.2s;
}

.new-chat-btn:hover {
    background-color: #404040;
    border-color: #10a37f;
}

.chat-history {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
}

.chat-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    margin-bottom: 4px;
    transition: background-color 0.2s;
    position: relative;
}

.chat-item:hover {
    background-color: #404040;
}

.chat-item.active {
    background-color: #10a37f;
}

.chat-item i {
    font-size: 14px;
    color: #888;
}

.chat-item.active i {
    color: white;
}

.chat-item span {
    flex: 1;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chat-options {
    background: none;
    border: none;
    color: #888;
    cursor: pointer;
    padding: 4px;
    opacity: 0;
    transition: opacity 0.2s;
}

.chat-item:hover .chat-options {
    opacity: 1;
}

.sidebar-footer {
    padding: 16px;
    border-top: 1px solid #404040;
}

.sidebar-btn {
    width: 100%;
    background: none;
    border: none;
    color: #ffffff;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px;
    margin-bottom: 8px;
    transition: background-color 0.2s;
}

.sidebar-btn:hover {
    background-color: #404040;
}

/* 主聊天区域 */
.chat-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #212121;
}

.chat-header {
    text-align: center;
    padding: 40px 20px;
    border-bottom: 1px solid #404040;
}

.chat-header h1 {
    font-size: 32px;
    margin-bottom: 8px;
    background: linear-gradient(135deg, #10a37f, #0d8f6b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.chat-header p {
    color: #888;
    font-size: 16px;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 消息样式 */
.message {
    display: flex;
    gap: 12px;
    max-width: 100%;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.user-message .message-avatar {
    background-color: #10a37f;
    color: white;
}

.ai-message .message-avatar {
    background-color: #404040;
    color: white;
}

.message-content {
    flex: 1;
    background-color: #2a2a2a;
    padding: 16px;
    border-radius: 12px;
    position: relative;
}

.user-message .message-content {
    background-color: #10a37f;
    margin-left: auto;
    max-width: 70%;
}

.ai-message .message-content {
    background-color: #2a2a2a;
    max-width: 85%;
}

.message-content p {
    margin-bottom: 12px;
    line-height: 1.6;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.message-content ul {
    margin: 12px 0;
    padding-left: 20px;
}

.message-content li {
    margin-bottom: 8px;
    line-height: 1.6;
}

.message-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
    opacity: 0;
    transition: opacity 0.2s;
}

.ai-message:hover .message-actions {
    opacity: 1;
}

.action-btn {
    background: none;
    border: none;
    color: #888;
    cursor: pointer;
    padding: 6px;
    border-radius: 4px;
    font-size: 12px;
    transition: all 0.2s;
}

.action-btn:hover {
    background-color: #404040;
    color: #ffffff;
}

/* 输入区域 */
.input-container {
    padding: 20px;
    border-top: 1px solid #404040;
    background-color: #212121;
}

.input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 12px;
    background-color: #2a2a2a;
    border-radius: 12px;
    padding: 12px 16px;
    border: 1px solid #404040;
    transition: border-color 0.2s;
}

.input-wrapper:focus-within {
    border-color: #10a37f;
}

#messageInput {
    flex: 1;
    background: none;
    border: none;
    color: #ffffff;
    font-size: 16px;
    resize: none;
    outline: none;
    min-height: 24px;
    max-height: 120px;
    line-height: 1.5;
}

#messageInput::placeholder {
    color: #888;
}

.send-btn {
    background-color: #10a37f;
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    flex-shrink: 0;
}

.send-btn:hover {
    background-color: #0d8f6b;
    transform: scale(1.05);
}

.send-btn:disabled {
    background-color: #404040;
    cursor: not-allowed;
    transform: none;
}

.input-footer {
    text-align: center;
    margin-top: 12px;
}

.input-footer small {
    color: #888;
    font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: 0;
        top: 60px;
        height: calc(100vh - 60px);
        z-index: 999;
        transform: translateX(-100%);
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .chat-main {
        width: 100%;
    }
    
    .message-content {
        max-width: 90% !important;
    }
}

/* 打字指示器 */
.typing-indicator .message-content {
    padding: 16px 20px;
}

.typing-dots {
    display: flex;
    gap: 4px;
    align-items: center;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #888;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
    animation-delay: 0s;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.4;
    }
    30% {
        transform: translateY(-10px);
        opacity: 1;
    }
}

/* 消息淡入动画 */
.message {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #2a2a2a;
}

::-webkit-scrollbar-thumb {
    background: #404040;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}
