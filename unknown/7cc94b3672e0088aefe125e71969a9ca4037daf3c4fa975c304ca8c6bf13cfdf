# ChatGPT 界面设计

一个完全仿照 ChatGPT 官方界面设计的前端项目，使用纯 HTML、CSS 和 JavaScript 实现。

## 📁 项目结构

```
chatgpt-interface/
├── index.html          # 主页面文件
├── styles.css          # 样式文件
├── script.js           # 交互逻辑文件
└── README.md           # 项目说明文档
```

## 🎨 设计特点

### 界面布局
- **顶部导航栏**: 品牌Logo、升级按钮、设置和用户头像
- **左侧边栏**: 新建聊天、聊天历史、设置选项
- **主聊天区域**: 消息显示区域，支持用户和AI消息
- **底部输入区**: 消息输入框和发送按钮

### 视觉设计
- **深色主题**: 主要使用黑色和深灰色背景
- **OpenAI绿色**: 使用 #10a37f 作为主要强调色
- **现代字体**: Inter 字体系列，简洁现代
- **流畅动画**: 消息淡入、按钮悬停等交互动画

## 🚀 功能特性

### 基础功能
- ✅ 响应式设计（支持移动端和桌面端）
- ✅ 侧边栏收起/展开
- ✅ 消息发送和接收
- ✅ 聊天历史管理
- ✅ 新建聊天功能

### 交互功能
- ✅ 消息复制功能
- ✅ 消息点赞/点踩
- ✅ 打字指示器动画
- ✅ 自动滚动到最新消息
- ✅ 输入框自动调整高度
- ✅ 回车发送，Shift+回车换行

### 视觉效果
- ✅ 消息淡入动画
- ✅ 按钮悬停效果
- ✅ 自定义滚动条样式
- ✅ 流畅的过渡动画

## 🛠️ 技术栈

- **HTML5**: 语义化标签，无障碍访问
- **CSS3**: Flexbox布局，CSS动画，响应式设计
- **JavaScript**: 原生ES6+，DOM操作，事件处理
- **Font Awesome**: 图标库

## 📱 响应式支持

- **桌面端**: 完整的侧边栏和主聊天区域
- **平板端**: 自适应布局调整
- **移动端**: 侧边栏自动隐藏，触摸友好的交互

## 🎯 使用方法

1. 直接在浏览器中打开 `index.html` 文件
2. 或者使用本地服务器：
   ```bash
   # 使用 Python
   python -m http.server 8000
   
   # 使用 Node.js
   npx serve .
   ```
3. 在浏览器中访问 `http://localhost:8000`

## 🎨 自定义配置

### 修改主题色
在 `styles.css` 中修改以下变量：
```css
/* 主要强调色 */
#10a37f -> 您的颜色

/* 背景色 */
#212121 -> 您的背景色
#171717 -> 您的侧边栏背景色
```

### 添加新功能
在 `script.js` 中可以扩展以下功能：
- 消息持久化存储
- 用户认证
- 实际的AI接口集成
- 文件上传功能

## 📄 许可证

本项目仅供学习和参考使用，请勿用于商业用途。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

---

**注意**: 这是一个纯前端的界面演示项目，不包含实际的AI功能。如需集成真实的AI服务，请参考相关API文档。
