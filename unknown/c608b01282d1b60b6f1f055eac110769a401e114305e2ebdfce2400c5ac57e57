<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGPT</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="header-left">
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
            <div class="logo">
                <i class="fas fa-robot"></i>
                <span>ChatGPT</span>
            </div>
        </div>
        <div class="header-right">
            <button class="upgrade-btn">升级到 Plus</button>
            <button class="settings-btn">
                <i class="fas fa-cog"></i>
            </button>
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>
        </div>
    </header>

    <div class="main-container">
        <!-- 侧边栏 -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <button class="new-chat-btn">
                    <i class="fas fa-plus"></i>
                    新建聊天
                </button>
            </div>
            
            <div class="chat-history">
                <div class="chat-item active">
                    <i class="fas fa-message"></i>
                    <span>关于前端开发的问题</span>
                    <button class="chat-options">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                </div>
                <div class="chat-item">
                    <i class="fas fa-message"></i>
                    <span>Python 数据分析</span>
                    <button class="chat-options">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                </div>
                <div class="chat-item">
                    <i class="fas fa-message"></i>
                    <span>机器学习算法解释</span>
                    <button class="chat-options">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                </div>
                <div class="chat-item">
                    <i class="fas fa-message"></i>
                    <span>写作技巧指导</span>
                    <button class="chat-options">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                </div>
            </div>

            <div class="sidebar-footer">
                <button class="sidebar-btn">
                    <i class="fas fa-question-circle"></i>
                    帮助与常见问题
                </button>
                <button class="sidebar-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    退出登录
                </button>
            </div>
        </aside>

        <!-- 主聊天区域 -->
        <main class="chat-main">
            <div class="chat-header">
                <h1>ChatGPT</h1>
                <p>我是您的AI助手，有什么可以帮助您的吗？</p>
            </div>

            <div class="chat-messages" id="chatMessages">
                <!-- 示例消息 -->
                <div class="message user-message">
                    <div class="message-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="message-content">
                        <p>你好！能帮我解释一下什么是前端开发吗？</p>
                    </div>
                </div>

                <div class="message ai-message">
                    <div class="message-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <p>你好！我很乐意为你解释前端开发。</p>
                        <p>前端开发是指创建用户直接交互的网站或应用程序界面的过程。主要包括：</p>
                        <ul>
                            <li><strong>HTML</strong>：网页的结构和内容</li>
                            <li><strong>CSS</strong>：网页的样式和布局</li>
                            <li><strong>JavaScript</strong>：网页的交互功能</li>
                        </ul>
                        <p>前端开发者需要确保网站在不同设备和浏览器上都能正常显示和运行。</p>
                        <div class="message-actions">
                            <button class="action-btn copy-btn">
                                <i class="fas fa-copy"></i>
                            </button>
                            <button class="action-btn like-btn">
                                <i class="fas fa-thumbs-up"></i>
                            </button>
                            <button class="action-btn dislike-btn">
                                <i class="fas fa-thumbs-down"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="input-container">
                <div class="input-wrapper">
                    <textarea 
                        id="messageInput" 
                        placeholder="输入消息..." 
                        rows="1"
                    ></textarea>
                    <button class="send-btn" id="sendBtn">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
                <div class="input-footer">
                    <small>ChatGPT 可能会犯错。请核实重要信息。</small>
                </div>
            </div>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>
