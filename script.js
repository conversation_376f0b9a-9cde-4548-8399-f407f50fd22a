// DOM 元素
const sidebarToggle = document.getElementById('sidebarToggle');
const sidebar = document.getElementById('sidebar');
const messageInput = document.getElementById('messageInput');
const sendBtn = document.getElementById('sendBtn');
const chatMessages = document.getElementById('chatMessages');

// 侧边栏切换功能
sidebarToggle.addEventListener('click', () => {
    sidebar.classList.toggle('open');
});

// 点击主区域时关闭侧边栏（移动端）
document.addEventListener('click', (e) => {
    if (window.innerWidth <= 768) {
        if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
            sidebar.classList.remove('open');
        }
    }
});

// 输入框自动调整高度
messageInput.addEventListener('input', () => {
    messageInput.style.height = 'auto';
    messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
    
    // 更新发送按钮状态
    updateSendButton();
});

// 更新发送按钮状态
function updateSendButton() {
    const hasText = messageInput.value.trim().length > 0;
    sendBtn.disabled = !hasText;
}

// 发送消息
function sendMessage() {
    const text = messageInput.value.trim();
    if (!text) return;
    
    // 添加用户消息
    addMessage(text, 'user');
    
    // 清空输入框
    messageInput.value = '';
    messageInput.style.height = 'auto';
    updateSendButton();
    
    // 模拟AI回复
    setTimeout(() => {
        const responses = [
            "这是一个很好的问题！让我来为您详细解答。",
            "我理解您的需求，这里有几个建议供您参考。",
            "根据您的描述，我认为可以从以下几个方面来考虑这个问题。",
            "感谢您的提问！这确实是一个值得深入探讨的话题。"
        ];
        const randomResponse = responses[Math.floor(Math.random() * responses.length)];
        addMessage(randomResponse, 'ai');
    }, 1000);
}

// 添加消息到聊天区域
function addMessage(text, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}-message`;
    
    const avatarDiv = document.createElement('div');
    avatarDiv.className = 'message-avatar';
    avatarDiv.innerHTML = type === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    
    const textP = document.createElement('p');
    textP.textContent = text;
    contentDiv.appendChild(textP);
    
    // 为AI消息添加操作按钮
    if (type === 'ai') {
        const actionsDiv = document.createElement('div');
        actionsDiv.className = 'message-actions';
        actionsDiv.innerHTML = `
            <button class="action-btn copy-btn" onclick="copyMessage(this)">
                <i class="fas fa-copy"></i>
            </button>
            <button class="action-btn like-btn" onclick="likeMessage(this)">
                <i class="fas fa-thumbs-up"></i>
            </button>
            <button class="action-btn dislike-btn" onclick="dislikeMessage(this)">
                <i class="fas fa-thumbs-down"></i>
            </button>
        `;
        contentDiv.appendChild(actionsDiv);
    }
    
    messageDiv.appendChild(avatarDiv);
    messageDiv.appendChild(contentDiv);
    
    chatMessages.appendChild(messageDiv);
    
    // 滚动到底部
    chatMessages.scrollTop = chatMessages.scrollHeight;
    
    // 添加淡入动画
    messageDiv.style.opacity = '0';
    messageDiv.style.transform = 'translateY(20px)';
    setTimeout(() => {
        messageDiv.style.transition = 'all 0.3s ease';
        messageDiv.style.opacity = '1';
        messageDiv.style.transform = 'translateY(0)';
    }, 10);
}

// 复制消息内容
function copyMessage(btn) {
    const messageContent = btn.closest('.message-content').querySelector('p').textContent;
    navigator.clipboard.writeText(messageContent).then(() => {
        // 显示复制成功提示
        const originalIcon = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check"></i>';
        btn.style.color = '#10a37f';
        setTimeout(() => {
            btn.innerHTML = originalIcon;
            btn.style.color = '';
        }, 2000);
    });
}

// 点赞消息
function likeMessage(btn) {
    btn.style.color = btn.style.color === 'rgb(16, 163, 127)' ? '' : '#10a37f';
    // 如果点赞，取消点踩
    const dislikeBtn = btn.parentElement.querySelector('.dislike-btn');
    dislikeBtn.style.color = '';
}

// 点踩消息
function dislikeMessage(btn) {
    btn.style.color = btn.style.color === 'rgb(239, 68, 68)' ? '' : '#ef4444';
    // 如果点踩，取消点赞
    const likeBtn = btn.parentElement.querySelector('.like-btn');
    likeBtn.style.color = '';
}

// 发送按钮点击事件
sendBtn.addEventListener('click', sendMessage);

// 回车发送消息
messageInput.addEventListener('keydown', (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        sendMessage();
    }
});

// 聊天历史点击事件
document.querySelectorAll('.chat-item').forEach(item => {
    item.addEventListener('click', () => {
        // 移除其他项的active类
        document.querySelectorAll('.chat-item').forEach(i => i.classList.remove('active'));
        // 添加active类到当前项
        item.classList.add('active');
        
        // 这里可以加载对应的聊天历史
        loadChatHistory(item.querySelector('span').textContent);
    });
});

// 加载聊天历史（模拟）
function loadChatHistory(chatTitle) {
    // 清空当前消息
    chatMessages.innerHTML = '';
    
    // 模拟加载历史消息
    setTimeout(() => {
        addMessage(`正在查看"${chatTitle}"的聊天记录`, 'ai');
    }, 300);
}

// 新建聊天
document.querySelector('.new-chat-btn').addEventListener('click', () => {
    // 清空消息区域
    chatMessages.innerHTML = '';
    
    // 移除所有聊天项的active状态
    document.querySelectorAll('.chat-item').forEach(item => {
        item.classList.remove('active');
    });
    
    // 显示欢迎消息
    setTimeout(() => {
        addMessage('您好！我是ChatGPT，一个AI助手。有什么可以帮助您的吗？', 'ai');
    }, 300);
});

// 窗口大小改变时的处理
window.addEventListener('resize', () => {
    if (window.innerWidth > 768) {
        sidebar.classList.remove('open');
    }
});

// 初始化
updateSendButton();

// 添加一些示例交互
document.addEventListener('DOMContentLoaded', () => {
    // 为设置按钮添加点击事件
    document.querySelector('.settings-btn').addEventListener('click', () => {
        alert('设置功能开发中...');
    });
    
    // 为升级按钮添加点击事件
    document.querySelector('.upgrade-btn').addEventListener('click', () => {
        alert('升级到ChatGPT Plus功能开发中...');
    });
    
    // 为用户头像添加点击事件
    document.querySelector('.user-avatar').addEventListener('click', () => {
        alert('用户菜单开发中...');
    });
    
    // 为侧边栏按钮添加点击事件
    document.querySelectorAll('.sidebar-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const text = btn.textContent.trim();
            alert(`${text}功能开发中...`);
        });
    });
});

// 添加打字机效果（可选）
function typeWriter(element, text, speed = 50) {
    let i = 0;
    element.textContent = '';
    
    function type() {
        if (i < text.length) {
            element.textContent += text.charAt(i);
            i++;
            setTimeout(type, speed);
        }
    }
    
    type();
}

// 模拟加载状态
function showTypingIndicator() {
    const typingDiv = document.createElement('div');
    typingDiv.className = 'message ai-message typing-indicator';
    typingDiv.innerHTML = `
        <div class="message-avatar">
            <i class="fas fa-robot"></i>
        </div>
        <div class="message-content">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    `;
    
    chatMessages.appendChild(typingDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
    
    return typingDiv;
}

// 移除打字指示器
function removeTypingIndicator(indicator) {
    if (indicator && indicator.parentNode) {
        indicator.parentNode.removeChild(indicator);
    }
}
